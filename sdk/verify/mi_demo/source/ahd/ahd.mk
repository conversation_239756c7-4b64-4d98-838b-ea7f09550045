INC  += $(DB_BUILD_TOP)/internal/live555/UsageEnvironment/include
INC  += $(DB_BUILD_TOP)/internal/live555/groupsock/include
INC  += $(DB_BUILD_TOP)/internal/live555/liveMedia/include
INC  += $(DB_BUILD_TOP)/internal/live555/BasicUsageEnvironment/include
INC  += $(DB_BUILD_TOP)/internal/live555/mediaServer/include
INC  += $(DB_BUILD_TOP)/internal/iniparser

ST_DEP := common venc hdmi live555 iniparser disp

LIBS += -L./internal/ldc
LIBS += -lmi_sensor -lmi_vif -lmi_isp -lmi_scl -lmi_venc -lmi_disp -lmi_hdmi -lmi_panel -lmi_vdec -lmi_vpe -lmi_ai -lmi_rgn


ifneq ($(CHIP), p3)
INC += ./internal/ldc
LIBS +=-leptz
endif

SUPPORT_MI_ISP = TRUE
ifeq ($(SUPPORT_MI_ISP), TRUE)
CODEDEFINE += -DBUILD_MI_ISP=1
INC  += ./internal/cus3a
ST_DEP += cus3a
LIBS += -lmi_isp -lmi_iqserver -lcus3a -lispalgo
LIBS +=-lfbc_decode
endif

ifeq ($(CHIP), m6)
LIBS += -lmi_jpd
CODEDEFINE += -DBUILD_MI_JPD=1
ST_DEP +=dh9931
ST_DEP +=tp2830
INC += ./internal/dh9931
INC += ./internal/tp2830
LIBS += -L./internal/dh9931
LIBS +=-ldh9931_sdk
endif