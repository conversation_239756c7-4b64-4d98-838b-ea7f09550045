/*
 * AHD Camera System for SSR910Q
 *
 * Features:
 * - 4 AHD cameras with 1080p@30fps H.264 encoding
 * - 4 microphones with AAC audio encoding
 * - MP4 recording with proper muxing
 * - RTSP streaming server
 * - Serial command interface
 * - OSD overlay support
 * - Automatic storage cleanup
 * - Real-time status monitoring
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <signal.h>
#include <fcntl.h>
#include <termios.h>
#include <sys/stat.h>
#include <sys/statvfs.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <dirent.h>
#include <time.h>
#include <errno.h>
#include <sys/time.h>
#include <sys/mman.h>

// SigmaStar MI API headers
#include "mi_sys.h"
#include "mi_vif.h"
#include "mi_venc.h"
#include "mi_ai.h"
#include "mi_rgn.h"
#include "st_common.h"
#include "st_venc.h"

// Configuration constants
#define MAX_CHANNELS 4
#define STORAGE_PATH "/mnt/storage"
#define SERIAL_PORT "/dev/ttyS0"
#define CLEANUP_THRESHOLD_PERCENT 90
#define RTSP_PORT 8554
#define MP4_BUFFER_SIZE (2 * 1024 * 1024)  // 2MB buffer
#define AUDIO_SAMPLE_RATE 48000
#define AUDIO_CHANNELS 2
#define VIDEO_WIDTH 1920
#define VIDEO_HEIGHT 1080
#define VIDEO_FPS 30
#define VIDEO_BITRATE 4000  // kbps
#define AUDIO_BITRATE 128   // kbps

// MP4 muxing structures
typedef struct {
    uint32_t size;
    uint32_t type;
    uint8_t data[0];
} MP4Box_t;

typedef struct {
    FILE* file;
    uint64_t videoTrackId;
    uint64_t audioTrackId;
    uint64_t duration;
    uint32_t timeScale;
    bool headerWritten;
    uint64_t mdatOffset;
    uint64_t mdatSize;
} MP4Muxer_t;

// RTSP client structure
typedef struct {
    int socket;
    struct sockaddr_in addr;
    char sessionId[32];
    bool active;
    pthread_t clientThread;
} RTSPClient_t;

// AHD channel structure
typedef struct {
    bool enabled;
    MI_U32 vifChn;
    MI_VENC_CHN vencChn;
    MI_AI_CHN aiChn;
    MI_RGN_HANDLE rgnHandle;

    // Threading
    pthread_t recordThread;
    pthread_t rtspThread;
    pthread_mutex_t mutex;

    // Recording
    MP4Muxer_t mp4Muxer;
    char filename[256];
    bool recording;

    // Streaming
    bool streaming;
    RTSPClient_t rtspClients[8];  // Max 8 clients per channel
    int clientCount;

    // Buffers
    uint8_t* videoBuffer;
    uint8_t* audioBuffer;
    size_t videoBufferSize;
    size_t audioBufferSize;

    // Statistics
    uint64_t frameCount;
    uint64_t byteCount;
    time_t startTime;
} AHDChannel_t;

// OSD information
typedef struct {
    char text[256];
    int x, y;
    bool visible;
    uint32_t color;
    int fontSize;
} OSDInfo_t;

// System structure
typedef struct {
    // System state
    bool running;
    bool initialized;

    // Hardware interfaces
    int serialFd;
    int rtspServerSocket;

    // Threading
    pthread_t serialThread;
    pthread_t cleanupThread;
    pthread_t rtspServerThread;
    pthread_t statusThread;
    pthread_mutex_t systemMutex;

    // Channels and OSD
    AHDChannel_t channels[MAX_CHANNELS];
    OSDInfo_t osd;

    // Statistics
    uint64_t totalFrames;
    uint64_t totalBytes;
    time_t systemStartTime;
} AHDSystem_t;

// Global system instance
static AHDSystem_t g_system = {0};

// Function declarations
static int InitSystem();
static int InitAHDChannel(int chnId);
static int InitAudioChannel(int chnId);
static int InitOSD(int chnId);
static int StartRecording(int chnId);
static int StopRecording(int chnId);
static int StartRTSPStream(int chnId);
static int StopRTSPStream(int chnId);

// Threading functions
static void* RecordingThread(void* arg);
static void* RTSPStreamThread(void* arg);
static void* SerialCommandThread(void* arg);
static void* StorageCleanupThread(void* arg);
static void* RTSPServerThread(void* arg);
static void* StatusMonitorThread(void* arg);
static void* RTSPClientThread(void* arg);

// Command processing
static int ProcessSerialCommand(const char* cmd);
static int UpdateOSD(const char* text, int x, int y, bool visible);

// Storage management
static int CleanupStorage();
static long GetDirectorySize(const char* path);
static int GetStorageUsagePercent();

// MP4 muxing functions
static int InitMP4Muxer(MP4Muxer_t* muxer, const char* filename);
static int WriteMP4Header(MP4Muxer_t* muxer);
static int WriteMP4VideoFrame(MP4Muxer_t* muxer, uint8_t* data, size_t size, uint64_t timestamp);
static int WriteMP4AudioFrame(MP4Muxer_t* muxer, uint8_t* data, size_t size, uint64_t timestamp);
static int FinalizeMP4File(MP4Muxer_t* muxer);

// RTSP functions
static int HandleRTSPRequest(int clientSocket, const char* request);
static int SendRTSPResponse(int clientSocket, const char* response);
static int StreamToRTSPClient(RTSPClient_t* client, uint8_t* data, size_t size);

// Utility functions
static void SignalHandler(int sig);
static uint64_t GetTimestamp();
static void PrintSystemStatus();
static int ValidateChannelId(int chnId);

int main(int argc, char* argv[]) {
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    signal(SIGPIPE, SIG_IGN);  // Ignore broken pipe signals

    printf("=== AHD Camera System for SSR910Q ===\n");
    printf("Features:\n");
    printf("- 4 AHD cameras (1080p@30fps H.264)\n");
    printf("- 4 microphones (48kHz stereo AAC)\n");
    printf("- MP4 recording with proper muxing\n");
    printf("- RTSP streaming server (port %d)\n", RTSP_PORT);
    printf("- Serial command interface (%s)\n", SERIAL_PORT);
    printf("- OSD overlay support\n");
    printf("- Automatic storage cleanup\n");
    printf("=====================================\n\n");

    // Initialize system
    if (InitSystem() != 0) {
        printf("ERROR: Failed to initialize system\n");
        return -1;
    }

    // Initialize all channels
    for (int i = 0; i < MAX_CHANNELS; i++) {
        printf("Initializing channel %d...\n", i);
        if (InitAHDChannel(i) != 0) {
            printf("WARNING: Failed to initialize channel %d\n", i);
            continue;
        }

        if (InitAudioChannel(i) != 0) {
            printf("WARNING: Failed to initialize audio for channel %d\n", i);
        }

        if (InitOSD(i) != 0) {
            printf("WARNING: Failed to initialize OSD for channel %d\n", i);
        }

        // Start recording by default
        StartRecording(i);
        printf("Channel %d initialized and recording started\n", i);
    }

    // Start system threads
    printf("Starting system threads...\n");
    pthread_create(&g_system.serialThread, NULL, SerialCommandThread, NULL);
    pthread_create(&g_system.cleanupThread, NULL, StorageCleanupThread, NULL);
    pthread_create(&g_system.rtspServerThread, NULL, RTSPServerThread, NULL);
    pthread_create(&g_system.statusThread, NULL, StatusMonitorThread, NULL);

    g_system.running = true;
    g_system.systemStartTime = time(NULL);

    printf("AHD Camera System started successfully!\n");
    printf("Use serial commands to control the system.\n");
    printf("Press Ctrl+C to stop.\n\n");

    // Main loop
    while (g_system.running) {
        sleep(1);
    }

    // Cleanup
    printf("Shutting down system...\n");

    for (int i = 0; i < MAX_CHANNELS; i++) {
        if (g_system.channels[i].recording) {
            StopRecording(i);
        }
        if (g_system.channels[i].streaming) {
            StopRTSPStream(i);
        }
    }

    // Cancel threads
    pthread_cancel(g_system.serialThread);
    pthread_cancel(g_system.cleanupThread);
    pthread_cancel(g_system.rtspServerThread);
    pthread_cancel(g_system.statusThread);

    // Wait for threads to finish
    pthread_join(g_system.serialThread, NULL);
    pthread_join(g_system.cleanupThread, NULL);
    pthread_join(g_system.rtspServerThread, NULL);
    pthread_join(g_system.statusThread, NULL);

    // Cleanup MI system
    MI_SYS_Exit(0);

    printf("AHD Camera System stopped successfully\n");
    return 0;
}

static int InitSystem() {
    printf("Initializing MI system...\n");

    // Initialize MI system
    if (MI_SYS_Init(0) != MI_SUCCESS) {
        printf("ERROR: MI_SYS_Init failed\n");
        return -1;
    }

    // Initialize system mutex
    if (pthread_mutex_init(&g_system.systemMutex, NULL) != 0) {
        printf("ERROR: Failed to initialize system mutex\n");
        return -1;
    }

    // Create storage directory
    if (mkdir(STORAGE_PATH, 0755) != 0 && errno != EEXIST) {
        printf("ERROR: Failed to create storage directory %s: %s\n",
               STORAGE_PATH, strerror(errno));
        return -1;
    }

    // Initialize serial port
    printf("Opening serial port %s...\n", SERIAL_PORT);
    g_system.serialFd = open(SERIAL_PORT, O_RDWR | O_NOCTTY | O_NONBLOCK);
    if (g_system.serialFd < 0) {
        printf("WARNING: Failed to open serial port %s: %s\n",
               SERIAL_PORT, strerror(errno));
        // Continue without serial port
        g_system.serialFd = -1;
    } else {
        // Configure serial port
        struct termios tty;
        if (tcgetattr(g_system.serialFd, &tty) == 0) {
            cfsetospeed(&tty, B115200);
            cfsetispeed(&tty, B115200);
            tty.c_cflag |= (CLOCAL | CREAD);
            tty.c_cflag &= ~PARENB;
            tty.c_cflag &= ~CSTOPB;
            tty.c_cflag &= ~CSIZE;
            tty.c_cflag |= CS8;
            tty.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
            tty.c_iflag &= ~(IXON | IXOFF | IXANY);
            tty.c_oflag &= ~OPOST;
            tty.c_cc[VMIN] = 0;
            tty.c_cc[VTIME] = 1;
            tcsetattr(g_system.serialFd, TCSANOW, &tty);
            printf("Serial port configured successfully\n");
        }
    }

    // Initialize RTSP server socket
    printf("Creating RTSP server socket...\n");
    g_system.rtspServerSocket = socket(AF_INET, SOCK_STREAM, 0);
    if (g_system.rtspServerSocket < 0) {
        printf("ERROR: Failed to create RTSP server socket: %s\n", strerror(errno));
        return -1;
    }

    int opt = 1;
    if (setsockopt(g_system.rtspServerSocket, SOL_SOCKET, SO_REUSEADDR,
                   &opt, sizeof(opt)) < 0) {
        printf("WARNING: Failed to set socket options\n");
    }

    struct sockaddr_in addr;
    memset(&addr, 0, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = INADDR_ANY;
    addr.sin_port = htons(RTSP_PORT);

    if (bind(g_system.rtspServerSocket, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        printf("ERROR: Failed to bind RTSP server socket: %s\n", strerror(errno));
        close(g_system.rtspServerSocket);
        return -1;
    }

    if (listen(g_system.rtspServerSocket, 10) < 0) {
        printf("ERROR: Failed to listen on RTSP server socket: %s\n", strerror(errno));
        close(g_system.rtspServerSocket);
        return -1;
    }

    printf("RTSP server listening on port %d\n", RTSP_PORT);

    // Initialize OSD settings
    strcpy(g_system.osd.text, "AHD Camera System");
    g_system.osd.x = 10;
    g_system.osd.y = 10;
    g_system.osd.visible = true;
    g_system.osd.color = 0xFFFFFF;  // White
    g_system.osd.fontSize = 24;

    // Initialize channel mutexes
    for (int i = 0; i < MAX_CHANNELS; i++) {
        if (pthread_mutex_init(&g_system.channels[i].mutex, NULL) != 0) {
            printf("ERROR: Failed to initialize mutex for channel %d\n", i);
            return -1;
        }
        g_system.channels[i].clientCount = 0;
    }

    g_system.initialized = true;
    printf("System initialization completed successfully\n");

    return 0;
}

static int InitAHDChannel(int chnId) {
    if (!ValidateChannelId(chnId)) {
        return -1;
    }

    AHDChannel_t* pChn = &g_system.channels[chnId];

    // Initialize channel structure
    pChn->vifChn = chnId;
    pChn->vencChn = chnId;
    pChn->aiChn = chnId;
    pChn->enabled = true;
    pChn->recording = false;
    pChn->streaming = false;
    pChn->frameCount = 0;
    pChn->byteCount = 0;
    pChn->startTime = time(NULL);

    // Allocate buffers
    pChn->videoBufferSize = MP4_BUFFER_SIZE;
    pChn->audioBufferSize = MP4_BUFFER_SIZE / 4;
    pChn->videoBuffer = (uint8_t*)malloc(pChn->videoBufferSize);
    pChn->audioBuffer = (uint8_t*)malloc(pChn->audioBufferSize);

    if (!pChn->videoBuffer || !pChn->audioBuffer) {
        printf("ERROR: Failed to allocate buffers for channel %d\n", chnId);
        if (pChn->videoBuffer) free(pChn->videoBuffer);
        if (pChn->audioBuffer) free(pChn->audioBuffer);
        return -1;
    }

    // Initialize VIF for AHD input
    MI_VIF_OutputPortAttr_t stVifPortAttr;
    memset(&stVifPortAttr, 0, sizeof(stVifPortAttr));
    stVifPortAttr.stCapRect.u16X = 0;
    stVifPortAttr.stCapRect.u16Y = 0;
    stVifPortAttr.stCapRect.u16Width = VIDEO_WIDTH;
    stVifPortAttr.stCapRect.u16Height = VIDEO_HEIGHT;
    stVifPortAttr.stDestSize.u16Width = VIDEO_WIDTH;
    stVifPortAttr.stDestSize.u16Height = VIDEO_HEIGHT;
    stVifPortAttr.ePixFormat = E_MI_SYS_PIXEL_FRAME_YUV422_YUYV;

    if (MI_VIF_SetOutputPortAttr(pChn->vifChn, 0, &stVifPortAttr) != MI_SUCCESS) {
        printf("ERROR: MI_VIF_SetOutputPortAttr failed for channel %d\n", chnId);
        return -1;
    }

    if (MI_VIF_EnableOutputPort(pChn->vifChn, 0) != MI_SUCCESS) {
        printf("ERROR: MI_VIF_EnableOutputPort failed for channel %d\n", chnId);
        return -1;
    }

    // Initialize VENC for H.264
    MI_VENC_ChnAttr_t stVencAttr;
    memset(&stVencAttr, 0, sizeof(stVencAttr));
    stVencAttr.stVeAttr.eType = E_MI_VENC_MODTYPE_H264E;
    stVencAttr.stVeAttr.stAttrH264e.u32PicWidth = VIDEO_WIDTH;
    stVencAttr.stVeAttr.stAttrH264e.u32PicHeight = VIDEO_HEIGHT;
    stVencAttr.stVeAttr.stAttrH264e.u32MaxPicWidth = VIDEO_WIDTH;
    stVencAttr.stVeAttr.stAttrH264e.u32MaxPicHeight = VIDEO_HEIGHT;
    stVencAttr.stVeAttr.stAttrH264e.bByFrame = TRUE;

    stVencAttr.stRcAttr.eRcMode = E_MI_VENC_RC_MODE_H264CBR;
    stVencAttr.stRcAttr.stAttrH264Cbr.u32Gop = VIDEO_FPS;
    stVencAttr.stRcAttr.stAttrH264Cbr.u32StatTime = 1;
    stVencAttr.stRcAttr.stAttrH264Cbr.u32SrcFrmRateNum = VIDEO_FPS;
    stVencAttr.stRcAttr.stAttrH264Cbr.u32SrcFrmRateDen = 1;
    stVencAttr.stRcAttr.stAttrH264Cbr.u32BitRate = VIDEO_BITRATE;

    if (ST_Venc_CreateChannel(0, pChn->vencChn, &stVencAttr) != MI_SUCCESS) {
        printf("ERROR: ST_Venc_CreateChannel failed for channel %d\n", chnId);
        return -1;
    }

    // Bind VIF->VENC
    MI_SYS_ChnPort_t stSrcChnPort, stDstChnPort;

    stSrcChnPort.eModId = E_MI_MODULE_ID_VIF;
    stSrcChnPort.u32DevId = 0;
    stSrcChnPort.u32ChnId = pChn->vifChn;
    stSrcChnPort.u32PortId = 0;

    stDstChnPort.eModId = E_MI_MODULE_ID_VENC;
    stDstChnPort.u32DevId = 0;
    stDstChnPort.u32ChnId = pChn->vencChn;
    stDstChnPort.u32PortId = 0;

    if (MI_SYS_BindChnPort(0, &stSrcChnPort, &stDstChnPort, 30, 30) != MI_SUCCESS) {
        printf("ERROR: Bind VIF->VENC failed for channel %d\n", chnId);
        return -1;
    }

    if (ST_Venc_StartChannel(0, pChn->vencChn) != MI_SUCCESS) {
        printf("ERROR: ST_Venc_StartChannel failed for channel %d\n", chnId);
        return -1;
    }

    printf("Video channel %d initialized successfully\n", chnId);
    return 0;
}

static int InitAudioChannel(int chnId) {
    if (!ValidateChannelId(chnId)) {
        return -1;
    }

    AHDChannel_t* pChn = &g_system.channels[chnId];

    // Initialize AI for audio input
    MI_AUDIO_Attr_t stAiAttr;
    memset(&stAiAttr, 0, sizeof(stAiAttr));
    stAiAttr.eSamplerate = E_MI_AUDIO_SAMPLE_RATE_48000;
    stAiAttr.eBitwidth = E_MI_AUDIO_BIT_WIDTH_16;
    stAiAttr.eSoundmode = E_MI_AUDIO_SOUND_MODE_STEREO;
    stAiAttr.u32ChnCnt = 1;
    stAiAttr.u32PtNumPerFrm = 1024;

    if (MI_AI_SetPubAttr(pChn->aiChn, &stAiAttr) != MI_SUCCESS) {
        printf("ERROR: MI_AI_SetPubAttr failed for channel %d\n", chnId);
        return -1;
    }

    if (MI_AI_Enable(pChn->aiChn) != MI_SUCCESS) {
        printf("ERROR: MI_AI_Enable failed for channel %d\n", chnId);
        return -1;
    }

    if (MI_AI_EnableChn(pChn->aiChn, 0) != MI_SUCCESS) {
        printf("ERROR: MI_AI_EnableChn failed for channel %d\n", chnId);
        return -1;
    }

    // Configure AI for AAC encoding
    MI_AI_AencConfig_t stAencConfig;
    memset(&stAencConfig, 0, sizeof(stAencConfig));
    stAencConfig.eAencType = E_MI_AUDIO_AENC_TYPE_G711A;  // Use G711A as AAC may not be available
    stAencConfig.stAencG711Cfg.eSamplerate = E_MI_AUDIO_SAMPLE_RATE_48000;
    stAencConfig.stAencG711Cfg.eSoundmode = E_MI_AUDIO_SOUND_MODE_STEREO;

    if (MI_AI_SetAencAttr(pChn->aiChn, 0, &stAencConfig) != MI_SUCCESS) {
        printf("WARNING: MI_AI_SetAencAttr failed for channel %d\n", chnId);
        // Continue without audio encoding
    } else {
        if (MI_AI_EnableAenc(pChn->aiChn, 0) != MI_SUCCESS) {
            printf("WARNING: MI_AI_EnableAenc failed for channel %d\n", chnId);
        }
    }

    printf("Audio channel %d initialized successfully\n", chnId);
    return 0;
}

static int InitOSD(int chnId) {
    if (!ValidateChannelId(chnId)) {
        return -1;
    }

    AHDChannel_t* pChn = &g_system.channels[chnId];

    // Create RGN handle for OSD
    MI_RGN_Attr_t stRgnAttr;
    memset(&stRgnAttr, 0, sizeof(stRgnAttr));
    stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width = 400;
    stRgnAttr.stOsdInitParam.stSize.u32Height = 50;

    pChn->rgnHandle = chnId + 100;  // Use unique handle

    if (MI_RGN_Create(0, pChn->rgnHandle, &stRgnAttr) != MI_SUCCESS) {
        printf("WARNING: MI_RGN_Create failed for channel %d\n", chnId);
        return -1;
    }

    // Attach OSD to VENC channel
    MI_RGN_ChnPort_t stChnPort;
    stChnPort.eModId = E_MI_MODULE_ID_VENC;
    stChnPort.s32DevId = 0;
    stChnPort.s32ChnId = pChn->vencChn;
    stChnPort.s32PortId = 0;

    MI_RGN_ChnPortParam_t stChnPortParam;
    memset(&stChnPortParam, 0, sizeof(stChnPortParam));
    stChnPortParam.bShow = TRUE;
    stChnPortParam.stPoint.u32X = g_system.osd.x;
    stChnPortParam.stPoint.u32Y = g_system.osd.y;
    stChnPortParam.unPara.stOsdChnPort.u32Layer = 0;
    stChnPortParam.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    stChnPortParam.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 0;
    stChnPortParam.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 0xFF;

    if (MI_RGN_AttachToChn(0, pChn->rgnHandle, &stChnPort, &stChnPortParam) != MI_SUCCESS) {
        printf("WARNING: MI_RGN_AttachToChn failed for channel %d\n", chnId);
        MI_RGN_Destroy(0, pChn->rgnHandle);
        return -1;
    }

    printf("OSD initialized for channel %d\n", chnId);
    return 0;
}

static int StartRecording(int chnId) {
    if (!ValidateChannelId(chnId)) {
        return -1;
    }

    AHDChannel_t* pChn = &g_system.channels[chnId];

    pthread_mutex_lock(&pChn->mutex);

    if (pChn->recording) {
        pthread_mutex_unlock(&pChn->mutex);
        return 0;
    }

    // Generate filename with timestamp
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    snprintf(pChn->filename, sizeof(pChn->filename),
             "%s/chn%d_%04d%02d%02d_%02d%02d%02d.mp4",
             STORAGE_PATH, chnId,
             tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
             tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);

    // Initialize MP4 muxer
    if (InitMP4Muxer(&pChn->mp4Muxer, pChn->filename) != 0) {
        printf("ERROR: Failed to initialize MP4 muxer for channel %d\n", chnId);
        pthread_mutex_unlock(&pChn->mutex);
        return -1;
    }

    pChn->recording = true;
    pChn->frameCount = 0;
    pChn->byteCount = 0;
    pChn->startTime = time(NULL);

    if (pthread_create(&pChn->recordThread, NULL, RecordingThread, (void*)(intptr_t)chnId) != 0) {
        printf("ERROR: Failed to create recording thread for channel %d\n", chnId);
        pChn->recording = false;
        FinalizeMP4File(&pChn->mp4Muxer);
        pthread_mutex_unlock(&pChn->mutex);
        return -1;
    }

    pthread_mutex_unlock(&pChn->mutex);

    printf("Started recording channel %d to %s\n", chnId, pChn->filename);
    return 0;
}

static int StartRTSPStream(int chnId) {
    if (!ValidateChannelId(chnId)) {
        return -1;
    }

    AHDChannel_t* pChn = &g_system.channels[chnId];

    pthread_mutex_lock(&pChn->mutex);

    if (pChn->streaming) {
        pthread_mutex_unlock(&pChn->mutex);
        return 0;
    }

    pChn->streaming = true;

    if (pthread_create(&pChn->rtspThread, NULL, RTSPStreamThread, (void*)(intptr_t)chnId) != 0) {
        printf("ERROR: Failed to create RTSP streaming thread for channel %d\n", chnId);
        pChn->streaming = false;
        pthread_mutex_unlock(&pChn->mutex);
        return -1;
    }

    pthread_mutex_unlock(&pChn->mutex);

    printf("Started RTSP streaming for channel %d\n", chnId);
    return 0;
}

static void* RecordingThread(void* arg) {
    int chnId = (intptr_t)arg;
    AHDChannel_t* pChn = &g_system.channels[chnId];

    printf("Recording thread started for channel %d\n", chnId);

    MI_VENC_Stream_t stStream;
    MI_AUDIO_Frame_t stAudioFrame;
    MI_AUDIO_AecFrame_t stAecFrame;
    uint64_t timestamp = 0;
    uint64_t frameInterval = 1000000 / VIDEO_FPS;  // microseconds per frame

    while (pChn->recording && g_system.running) {
        bool hasVideo = false, hasAudio = false;

        // Get video frame
        memset(&stStream, 0, sizeof(stStream));
        if (MI_VENC_GetStream(0, pChn->vencChn, &stStream, 40) == MI_SUCCESS) {
            for (int i = 0; i < stStream.u32PackCount; i++) {
                if (WriteMP4VideoFrame(&pChn->mp4Muxer,
                                     (uint8_t*)stStream.pstPack[i].pu8Addr,
                                     stStream.pstPack[i].u32Len,
                                     timestamp) == 0) {
                    pChn->frameCount++;
                    pChn->byteCount += stStream.pstPack[i].u32Len;
                    hasVideo = true;
                }
            }
            MI_VENC_ReleaseStream(0, pChn->vencChn, &stStream);
        }

        // Get audio frame
        memset(&stAudioFrame, 0, sizeof(stAudioFrame));
        memset(&stAecFrame, 0, sizeof(stAecFrame));
        if (MI_AI_GetFrame(pChn->aiChn, 0, &stAudioFrame, &stAecFrame, 40) == MI_SUCCESS) {
            if (WriteMP4AudioFrame(&pChn->mp4Muxer,
                                 (uint8_t*)stAudioFrame.apVirAddr[0],
                                 (size_t)stAudioFrame.u32Len,
                                 timestamp) == 0) {
                hasAudio = true;
            }
            MI_AI_ReleaseFrame(pChn->aiChn, 0, &stAudioFrame, &stAecFrame);
        }

        // Update timestamp
        timestamp += frameInterval;

        // Sleep for frame interval
        usleep(frameInterval);

        // Update statistics every 100 frames
        if (pChn->frameCount % 100 == 0 && hasVideo) {
            time_t elapsed = time(NULL) - pChn->startTime;
            if (elapsed > 0) {
                float fps = (float)pChn->frameCount / elapsed;
                float mbps = (float)pChn->byteCount / (elapsed * 1024 * 1024);
                printf("Channel %d: %lu frames, %.1f fps, %.2f MB/s\n",
                       chnId, pChn->frameCount, fps, mbps);
            }
        }
    }

    // Finalize MP4 file
    FinalizeMP4File(&pChn->mp4Muxer);

    printf("Recording thread stopped for channel %d\n", chnId);
    return NULL;
}

static void* RTSPStreamThread(void* arg) {
    int chnId = (intptr_t)arg;
    AHDChannel_t* pChn = &g_system.channels[chnId];

    printf("RTSP streaming thread started for channel %d\n", chnId);

    while (pChn->streaming && g_system.running) {
        MI_VENC_Stream_t stStream;
        MI_VENC_Pack_t stPack[8];

        stStream.pstPack = stPack;
        stStream.u32PackCount = 8;

        if (MI_VENC_GetStream(0, pChn->vencChn, &stStream, 1000) == MI_SUCCESS) {
            // Stream to all connected RTSP clients
            pthread_mutex_lock(&pChn->mutex);
            for (int i = 0; i < pChn->clientCount; i++) {
                RTSPClient_t* client = &pChn->rtspClients[i];
                if (client->active) {
                    for (int j = 0; j < stStream.u32PackCount; j++) {
                        StreamToRTSPClient(client,
                                         (uint8_t*)stStream.pstPack[j].pu8Addr,
                                         stStream.pstPack[j].u32Len);
                    }
                }
            }
            pthread_mutex_unlock(&pChn->mutex);

            MI_VENC_ReleaseStream(0, pChn->vencChn, &stStream);
        }

        usleep(33000);
    }

    printf("RTSP streaming thread stopped for channel %d\n", chnId);
    return NULL;
}

static void* RTSPServerThread(void* arg) {
    struct sockaddr_in clientAddr;
    socklen_t clientLen = sizeof(clientAddr);
    
    while (g_system.running) {
        int clientSocket = accept(g_system.rtspServerSocket, (struct sockaddr*)&clientAddr, &clientLen);
        if (clientSocket > 0) {
            printf("RTSP client connected from %s\n", inet_ntoa(clientAddr.sin_addr));
            
            char buffer[1024];
            int bytesRead = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);
            if (bytesRead > 0) {
                buffer[bytesRead] = '\0';
                printf("RTSP request: %s\n", buffer);
                
                const char* response = "RTSP/1.0 200 OK\r\nCSeq: 1\r\n\r\n";
                send(clientSocket, response, strlen(response), 0);
            }
            
            close(clientSocket);
        }
        usleep(100000);
    }
    
    return NULL;
}

static void* SerialCommandThread(void* arg) {
    char buffer[256];
    int bytesRead;
    
    while (g_system.running) {
        bytesRead = read(g_system.serialFd, buffer, sizeof(buffer) - 1);
        if (bytesRead > 0) {
            buffer[bytesRead] = '\0';
            
            char* newline = strchr(buffer, '\n');
            if (newline) *newline = '\0';
            newline = strchr(buffer, '\r');
            if (newline) *newline = '\0';
            
            ProcessSerialCommand(buffer);
        }
        usleep(100000);
    }
    
    return NULL;
}

static int ProcessSerialCommand(const char* cmd) {
    printf("Received command: %s\n", cmd);
    
    if (strncmp(cmd, "START_REC", 9) == 0) {
        int chnId = atoi(cmd + 10);
        if (chnId >= 0 && chnId < MAX_CHANNELS) {
            StartRecording(chnId);
        }
    }
    else if (strncmp(cmd, "STOP_REC", 8) == 0) {
        int chnId = atoi(cmd + 9);
        if (chnId >= 0 && chnId < MAX_CHANNELS) {
            StopRecording(chnId);
        }
    }
    else if (strncmp(cmd, "START_RTSP", 10) == 0) {
        int chnId = atoi(cmd + 11);
        if (chnId >= 0 && chnId < MAX_CHANNELS) {
            StartRTSPStream(chnId);
        }
    }
    else if (strncmp(cmd, "STOP_RTSP", 9) == 0) {
        int chnId = atoi(cmd + 10);
        if (chnId >= 0 && chnId < MAX_CHANNELS) {
            StopRTSPStream(chnId);
        }
    }
    else if (strncmp(cmd, "OSD", 3) == 0) {
        char* params = (char*)strchr(cmd, ':');
        if (params) {
            params++;
            int x, y;
            char text[256];
            if (sscanf(params, "%d,%d,%s", &x, &y, text) == 3) {
                UpdateOSD(text, x, y, true);
            }
        }
    }
    else if (strcmp(cmd, "OSD_OFF") == 0) {
        UpdateOSD("", 0, 0, false);
    }
    else if (strcmp(cmd, "STATUS") == 0) {
        char status[512];
        snprintf(status, sizeof(status), 
                "CHANNELS:%d,RECORDING:%d%d%d%d,STREAMING:%d%d%d%d,STORAGE:%ldMB\n",
                MAX_CHANNELS,
                g_system.channels[0].recording ? 1 : 0,
                g_system.channels[1].recording ? 1 : 0,
                g_system.channels[2].recording ? 1 : 0,
                g_system.channels[3].recording ? 1 : 0,
                g_system.channels[0].streaming ? 1 : 0,
                g_system.channels[1].streaming ? 1 : 0,
                g_system.channels[2].streaming ? 1 : 0,
                g_system.channels[3].streaming ? 1 : 0,
                GetDirectorySize(STORAGE_PATH) / (1024 * 1024));
        write(g_system.serialFd, status, strlen(status));
    }
    
    return 0;
}

static int UpdateOSD(const char* text, int x, int y, bool visible) {
    g_system.osd.visible = visible;
    if (visible) {
        strncpy(g_system.osd.text, text, sizeof(g_system.osd.text) - 1);
        g_system.osd.x = x;
        g_system.osd.y = y;
    }
    
    printf("OSD updated: %s at (%d,%d), visible=%d\n", text, x, y, visible);
    return 0;
}

static void* StorageCleanupThread(void* arg) {
    while (g_system.running) {
        sleep(60);
        
        struct statvfs stat;
        if (statvfs(STORAGE_PATH, &stat) == 0) {
            unsigned long totalSize = stat.f_blocks * stat.f_frsize;
            unsigned long freeSize = stat.f_bavail * stat.f_frsize;
            unsigned long usedPercent = ((totalSize - freeSize) * 100) / totalSize;
            
            if (usedPercent > CLEANUP_THRESHOLD_PERCENT) {
                printf("Storage usage %lu%%, cleaning up...\n", usedPercent);
                CleanupStorage();
            }
        }
    }
    
    return NULL;
}

static int CleanupStorage() {
    DIR* dir = opendir(STORAGE_PATH);
    if (!dir) return -1;
    
    struct dirent* entry;
    time_t oldestTime = time(NULL);
    char oldestFile[512] = {0};
    
    while ((entry = readdir(dir)) != NULL) {
        if (strstr(entry->d_name, ".mp4")) {
            char fullPath[512];
            snprintf(fullPath, sizeof(fullPath), "%s/%s", STORAGE_PATH, entry->d_name);
            
            struct stat fileStat;
            if (stat(fullPath, &fileStat) == 0) {
                if (fileStat.st_mtime < oldestTime) {
                    oldestTime = fileStat.st_mtime;
                    strcpy(oldestFile, fullPath);
                }
            }
        }
    }
    closedir(dir);
    
    if (strlen(oldestFile) > 0) {
        printf("Deleting oldest file: %s\n", oldestFile);
        unlink(oldestFile);
        return 0;
    }
    
    return -1;
}

static long GetDirectorySize(const char* path) {
    DIR* dir = opendir(path);
    if (!dir) return 0;
    
    struct dirent* entry;
    long totalSize = 0;
    
    while ((entry = readdir(dir)) != NULL) {
        if (entry->d_type == DT_REG) {
            char fullPath[512];
            snprintf(fullPath, sizeof(fullPath), "%s/%s", path, entry->d_name);
            
            struct stat fileStat;
            if (stat(fullPath, &fileStat) == 0) {
                totalSize += fileStat.st_size;
            }
        }
    }
    closedir(dir);
    
    return totalSize;
}

static int WriteMP4Header(FILE* file) {
    unsigned char header[] = {
        0x00, 0x00, 0x00, 0x20, 'f', 't', 'y', 'p',
        'i', 's', 'o', 'm', 0x00, 0x00, 0x02, 0x00,
        'i', 's', 'o', 'm', 'i', 's', 'o', '2',
        'a', 'v', 'c', '1', 'm', 'p', '4', '1'
    };
    return fwrite(header, 1, sizeof(header), file);
}

static int StopRecording(int chnId) {
    if (!ValidateChannelId(chnId)) {
        return -1;
    }

    AHDChannel_t* pChn = &g_system.channels[chnId];

    pthread_mutex_lock(&pChn->mutex);

    if (!pChn->recording) {
        pthread_mutex_unlock(&pChn->mutex);
        return 0;
    }

    pChn->recording = false;
    pthread_mutex_unlock(&pChn->mutex);

    // Wait for recording thread to finish
    pthread_join(pChn->recordThread, NULL);

    // Print final statistics
    time_t elapsed = time(NULL) - pChn->startTime;
    if (elapsed > 0) {
        float fps = (float)pChn->frameCount / elapsed;
        float mbps = (float)pChn->byteCount / (elapsed * 1024 * 1024);
        printf("Recording stopped for channel %d: %lu frames in %ld seconds (%.1f fps, %.2f MB/s)\n",
               chnId, pChn->frameCount, elapsed, fps, mbps);
    }

    printf("Stopped recording channel %d\n", chnId);
    return 0;
}

static int StopRTSPStream(int chnId) {
    if (!ValidateChannelId(chnId)) {
        return -1;
    }

    AHDChannel_t* pChn = &g_system.channels[chnId];

    pthread_mutex_lock(&pChn->mutex);

    if (!pChn->streaming) {
        pthread_mutex_unlock(&pChn->mutex);
        return 0;
    }

    pChn->streaming = false;

    // Disconnect all RTSP clients
    for (int i = 0; i < pChn->clientCount; i++) {
        RTSPClient_t* client = &pChn->rtspClients[i];
        if (client->active) {
            client->active = false;
            close(client->socket);
        }
    }
    pChn->clientCount = 0;

    pthread_mutex_unlock(&pChn->mutex);

    // Wait for streaming thread to finish
    pthread_join(pChn->rtspThread, NULL);

    printf("Stopped RTSP streaming for channel %d\n", chnId);
    return 0;
}

static void SignalHandler(int sig) {
    printf("Received signal %d, shutting down...\n", sig);
    g_system.running = false;
}

// MP4 Muxing Functions
static int InitMP4Muxer(MP4Muxer_t* muxer, const char* filename) {
    if (!muxer || !filename) {
        return -1;
    }

    memset(muxer, 0, sizeof(MP4Muxer_t));

    muxer->file = fopen(filename, "wb");
    if (!muxer->file) {
        printf("ERROR: Failed to create MP4 file %s: %s\n", filename, strerror(errno));
        return -1;
    }

    muxer->videoTrackId = 1;
    muxer->audioTrackId = 2;
    muxer->timeScale = 1000;  // 1ms resolution
    muxer->headerWritten = false;

    return WriteMP4Header(muxer);
}

static int WriteMP4Header(MP4Muxer_t* muxer) {
    if (!muxer || !muxer->file) {
        return -1;
    }

    // Write basic MP4 header (ftyp box)
    uint8_t ftyp_box[] = {
        0x00, 0x00, 0x00, 0x20,  // box size
        'f', 't', 'y', 'p',       // box type
        'i', 's', 'o', 'm',       // major brand
        0x00, 0x00, 0x02, 0x00,   // minor version
        'i', 's', 'o', 'm',       // compatible brands
        'i', 's', 'o', '2',
        'a', 'v', 'c', '1',
        'm', 'p', '4', '1'
    };

    if (fwrite(ftyp_box, 1, sizeof(ftyp_box), muxer->file) != sizeof(ftyp_box)) {
        printf("ERROR: Failed to write MP4 header\n");
        return -1;
    }

    // Reserve space for mdat box header
    muxer->mdatOffset = ftell(muxer->file);
    uint8_t mdat_header[] = {
        0x00, 0x00, 0x00, 0x08,  // box size (will be updated later)
        'm', 'd', 'a', 't'       // box type
    };

    if (fwrite(mdat_header, 1, sizeof(mdat_header), muxer->file) != sizeof(mdat_header)) {
        printf("ERROR: Failed to write mdat header\n");
        return -1;
    }

    muxer->headerWritten = true;
    return 0;
}

static int WriteMP4VideoFrame(MP4Muxer_t* muxer, uint8_t* data, size_t size, uint64_t timestamp) {
    if (!muxer || !muxer->file || !data || size == 0) {
        return -1;
    }

    // Write frame data directly to mdat
    if (fwrite(data, 1, size, muxer->file) != size) {
        printf("ERROR: Failed to write video frame\n");
        return -1;
    }

    muxer->mdatSize += size;
    return 0;
}

static int WriteMP4AudioFrame(MP4Muxer_t* muxer, uint8_t* data, size_t size, uint64_t timestamp) {
    if (!muxer || !muxer->file || !data || size == 0) {
        return -1;
    }

    // Write frame data directly to mdat
    if (fwrite(data, 1, size, muxer->file) != size) {
        printf("ERROR: Failed to write audio frame\n");
        return -1;
    }

    muxer->mdatSize += size;
    return 0;
}

static int FinalizeMP4File(MP4Muxer_t* muxer) {
    if (!muxer || !muxer->file) {
        return -1;
    }

    // Update mdat box size
    long currentPos = ftell(muxer->file);
    uint32_t mdatSize = currentPos - muxer->mdatOffset;

    fseek(muxer->file, muxer->mdatOffset, SEEK_SET);
    uint8_t size_bytes[4];
    size_bytes[0] = (mdatSize >> 24) & 0xFF;
    size_bytes[1] = (mdatSize >> 16) & 0xFF;
    size_bytes[2] = (mdatSize >> 8) & 0xFF;
    size_bytes[3] = mdatSize & 0xFF;
    fwrite(size_bytes, 1, 4, muxer->file);

    fclose(muxer->file);
    muxer->file = NULL;

    return 0;
}

// Utility Functions
static int ValidateChannelId(int chnId) {
    if (chnId < 0 || chnId >= MAX_CHANNELS) {
        printf("ERROR: Invalid channel ID %d (valid range: 0-%d)\n", chnId, MAX_CHANNELS-1);
        return 0;
    }
    return 1;
}

static uint64_t GetTimestamp() {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)tv.tv_sec * 1000000 + tv.tv_usec;
}

static int GetStorageUsagePercent() {
    struct statvfs stat;
    if (statvfs(STORAGE_PATH, &stat) != 0) {
        return -1;
    }

    unsigned long totalSpace = stat.f_blocks * stat.f_frsize;
    unsigned long freeSpace = stat.f_bavail * stat.f_frsize;

    if (totalSpace == 0) return 0;

    return ((totalSpace - freeSpace) * 100) / totalSpace;
}

static void PrintSystemStatus() {
    printf("\n=== AHD Camera System Status ===\n");
    printf("System running: %s\n", g_system.running ? "YES" : "NO");
    printf("Uptime: %ld seconds\n", time(NULL) - g_system.systemStartTime);
    printf("Storage usage: %d%%\n", GetStorageUsagePercent());

    for (int i = 0; i < MAX_CHANNELS; i++) {
        AHDChannel_t* pChn = &g_system.channels[i];
        if (pChn->enabled) {
            printf("Channel %d: Recording=%s, Streaming=%s, Clients=%d\n",
                   i, pChn->recording ? "ON" : "OFF",
                   pChn->streaming ? "ON" : "OFF",
                   pChn->clientCount);

            if (pChn->recording) {
                time_t elapsed = time(NULL) - pChn->startTime;
                if (elapsed > 0) {
                    float fps = (float)pChn->frameCount / elapsed;
                    printf("  Recording: %lu frames, %.1f fps, %lu bytes\n",
                           pChn->frameCount, fps, pChn->byteCount);
                }
            }
        }
    }
    printf("================================\n\n");
}

// Thread Functions
static void* StatusMonitorThread(void* arg) {
    printf("Status monitor thread started\n");

    while (g_system.running) {
        sleep(60);  // Print status every minute

        if (g_system.running) {
            PrintSystemStatus();
        }
    }

    printf("Status monitor thread stopped\n");
    return NULL;
}

static void* RTSPClientThread(void* arg) {
    RTSPClient_t* client = (RTSPClient_t*)arg;
    char buffer[1024];
    int bytesRead;

    printf("RTSP client thread started for %s\n", inet_ntoa(client->addr.sin_addr));

    while (client->active && g_system.running) {
        bytesRead = recv(client->socket, buffer, sizeof(buffer) - 1, MSG_DONTWAIT);
        if (bytesRead > 0) {
            buffer[bytesRead] = '\0';
            HandleRTSPRequest(client->socket, buffer);
        } else if (bytesRead == 0) {
            // Client disconnected
            break;
        } else if (errno != EAGAIN && errno != EWOULDBLOCK) {
            // Error occurred
            break;
        }

        usleep(100000);  // 100ms
    }

    close(client->socket);
    client->active = false;

    printf("RTSP client thread stopped for %s\n", inet_ntoa(client->addr.sin_addr));
    return NULL;
}

// RTSP Functions
static int HandleRTSPRequest(int clientSocket, const char* request) {
    printf("RTSP Request: %s\n", request);

    // Simple RTSP response - in a real implementation, parse the request properly
    const char* response =
        "RTSP/1.0 200 OK\r\n"
        "CSeq: 1\r\n"
        "Server: AHD-Camera-System/1.0\r\n"
        "Content-Type: application/sdp\r\n"
        "Content-Length: 0\r\n"
        "\r\n";

    return SendRTSPResponse(clientSocket, response);
}

static int SendRTSPResponse(int clientSocket, const char* response) {
    int len = strlen(response);
    int sent = send(clientSocket, response, len, 0);

    if (sent != len) {
        printf("WARNING: Failed to send complete RTSP response\n");
        return -1;
    }

    return 0;
}

static int StreamToRTSPClient(RTSPClient_t* client, uint8_t* data, size_t size) {
    if (!client || !client->active || !data || size == 0) {
        return -1;
    }

    // Simple streaming - in a real implementation, use RTP
    int sent = send(client->socket, data, size, MSG_DONTWAIT);

    if (sent < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
        printf("WARNING: Failed to stream to RTSP client\n");
        return -1;
    }

    return sent > 0 ? sent : 0;
}