#!/bin/bash

# Build script for AHD Camera System
# SSR910Q Platform

echo "Building AHD Camera System for SSR910Q..."

# Set build environment
export CHIP=SSR910Q
export DB_BUILD_TOP=$(pwd)/../../../..

# Include paths
INCLUDES="-I${DB_BUILD_TOP}/project/release/include"
INCLUDES="${INCLUDES} -I${DB_BUILD_TOP}/sdk/verify/mi_demo/source/internal/common"
INCLUDES="${INCLUDES} -I${DB_BUILD_TOP}/sdk/verify/mi_demo/source/internal/venc"
INCLUDES="${INCLUDES} -I${DB_BUILD_TOP}/sdk/verify/mi_demo/source/internal/rgn"

# Library paths
LIBPATHS="-L${DB_BUILD_TOP}/project/release/lib"

# Libraries
LIBS="-lmi_sys -lmi_vif -lmi_venc -lmi_ai -lmi_rgn"
LIBS="${LIBS} -lpthread -lm -lrt"

# Compiler flags
CFLAGS="-Wall -O2 -g"
CFLAGS="${CFLAGS} -DBUILD_MI_ISP=1"

# Source files
SOURCES="ahd.cpp"

# Output
OUTPUT="ahd_camera_system"

echo "Compiling..."
echo "g++ ${CFLAGS} ${INCLUDES} ${SOURCES} ${LIBPATHS} ${LIBS} -o ${OUTPUT}"

g++ ${CFLAGS} ${INCLUDES} ${SOURCES} ${LIBPATHS} ${LIBS} -o ${OUTPUT}

if [ $? -eq 0 ]; then
    echo "Build successful! Output: ${OUTPUT}"
    echo ""
    echo "Usage:"
    echo "  ./${OUTPUT}"
    echo ""
    echo "Serial Commands:"
    echo "  START_REC <channel>   - Start recording on channel (0-3)"
    echo "  STOP_REC <channel>    - Stop recording on channel"
    echo "  START_RTSP <channel>  - Start RTSP streaming on channel"
    echo "  STOP_RTSP <channel>   - Stop RTSP streaming on channel"
    echo "  OSD:<x>,<y>,<text>    - Update OSD overlay"
    echo "  STATUS                - Get system status"
    echo ""
    echo "RTSP URLs:"
    echo "  rtsp://<ip>:8554/channel0"
    echo "  rtsp://<ip>:8554/channel1"
    echo "  rtsp://<ip>:8554/channel2"
    echo "  rtsp://<ip>:8554/channel3"
else
    echo "Build failed!"
    exit 1
fi
