if INFINITY5

config SYS_CONFIG_NAME
    default "infinity5"
    ---help---
    To load the "include/configs/infinity5.h"


config SYS_SOC
    default "infinity5"
    ---help---
    To generate correct  "arch/arm/include/asm/arch"

config SYS_GENERIC_BOARD
    bool
    default y

config VERSION_FPGA
    bool "FPGA version"
    default n

config SSTAR_SPL_EARLY_BSS
    bool "clear bss section first before run board_f"
    default n
endif
