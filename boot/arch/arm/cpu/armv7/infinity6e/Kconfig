if INFINITY6E

config SYS_CONFIG_NAME
    default "infinity6e"
    ---help---
    To load the "include/configs/infinity6e.h"


config SYS_SOC
    default "infinity6e"
    ---help---
    To generate correct  "arch/arm/include/asm/arch"

config SYS_GENERIC_BOARD
    bool
    default y

config VERSION_FPGA
    bool "FPGA version"
    default n

config SS_BOOTING_PERF
    bool "booting time"
    default y

config SSTAR_SPL_EARLY_BSS
    bool "clear bss section first before run board_f"
    default n
endif
